name: 构建并发布docker镜像到ghcr.io

on:
  push:
    branches:
      - main
    tags:
      - v*  
  workflow_dispatch:  

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}  

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置QEMU
        uses: docker/setup-qemu-action@v3

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到GitHub容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取Docker元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=sha,prefix=git-
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=raw,value=latest  
      - name: 构建并推送Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: ./docker  
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  auto-trigger:
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 登录到GitHub容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 代码更改时自动重建Docker镜像
        run: |
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest || true
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest ./docker  
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
